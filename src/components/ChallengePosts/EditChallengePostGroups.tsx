import {Grid} from '@base-components';
import {useAppUserSafe, useEditChallengePostGroups} from '@contexts';
import type {ChallengePostGroups} from '@types';
import {PostCancelOrSend, PostImage, PostInput} from '../BasePosts';

type EditChallengePostV2Props = {
  initialState: ChallengePostGroups;
  isFirstCreate?: true;
  onSuccess: () => void;
  submitText: string;
};

export const EditChallengePostGroups: React.FC<EditChallengePostV2Props> = ({
  initialState,
  isFirstCreate = false,
  onSuccess,
  submitText,
}) => {
  const authAppUser = useAppUserSafe();
  const {
    isChanged,
    isImagePending,
    isPendingSubmit,
    onContentChange,
    onImageChange,
    onImagePaste,
    onReset,
    onSubmit,
    post,
  } = useEditChallengePostGroups(isFirstCreate, initialState, onSuccess);

  return (
    <Grid container>
      {isChanged && (
        <PostCancelOrSend
          isHideCancel={!isFirstCreate}
          isImagePending={isImagePending}
          isPendingSubmit={isPendingSubmit}
          isValid={isChanged}
          submitIcon={isFirstCreate ? 'message-arrow-right-outline' : 'content-save-outline'}
          submitLabel={submitText}
          onReset={onReset}
          onSubmit={onSubmit}
        />
      )}
      <PostInput
        hasPx
        hideName
        appUserProfile={authAppUser}
        label='Write a post'
        placeholder="What's happening?"
        post={post}
        onContentChange={onContentChange}
        onImagePaste={onImagePaste}
        onImageUrlChange={onImageChange}
      />
      <PostImage isImagePending={isImagePending} post={post} onImageChange={onImageChange} />
    </Grid>
  );
};
