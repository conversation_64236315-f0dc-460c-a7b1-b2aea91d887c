import {useCallback, useMemo, useState} from 'react';
import {useAddSnack, useAppUserSafe} from '@contexts';
import type {UUIDString} from '@types';
import {emptyChallengePostTeams} from '@utils';
import {EditChallengePostTeams} from './EditChallengePostTeams';

type CreateChallengePostTeamsProps = {
  challengeId: UUIDString;
  teamId: UUIDString | null;
};

export const CreateChallengePostTeams: React.FC<CreateChallengePostTeamsProps> = ({
  challengeId,
  teamId,
}) => {
  const appUser = useAppUserSafe();
  const addSnack = useAddSnack();

  // Memoize the initial empty post to prevent new IDs on every render
  const memoizedEmptyPost = useMemo(
    () => emptyChallengePostTeams(challengeId, appUser.id, teamId),
    [challengeId, appUser.id, teamId],
  );

  const [initialState, setInitialState] = useState(memoizedEmptyPost);

  const onSuccess = useCallback(() => {
    addSnack('Post created ✅');
    setInitialState(emptyChallengePostTeams(challengeId, appUser.id, teamId));
  }, [addSnack, appUser.id, challengeId, teamId]);

  return (
    <EditChallengePostTeams
      isFirstCreate
      initialState={initialState}
      submitText='Post'
      onSuccess={onSuccess}
    />
  );
};
