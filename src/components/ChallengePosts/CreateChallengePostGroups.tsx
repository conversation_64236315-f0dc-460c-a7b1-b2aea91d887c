import {useCallback, useMemo, useState} from 'react';
import {useAddSnack, useAppUserSafe} from '@contexts';
import type {UUIDString} from '@types';
import {emptyChallengePostGroups} from '@utils';
import {EditChallengePostGroups} from './EditChallengePostGroups';

type CreateChallengePostGroupsProps = {
  challengeId: UUIDString;
  groupId: UUIDString | null;
};

export const CreateChallengePostGroups: React.FC<CreateChallengePostGroupsProps> = ({
  challengeId,
  groupId,
}) => {
  const appUser = useAppUserSafe();
  const addSnack = useAddSnack();

  // Memoize the initial empty post to prevent new IDs on every render
  const memoizedEmptyPost = useMemo(
    () => emptyChallengePostGroups(challengeId, appUser.id, groupId),
    [challengeId, appUser.id, groupId],
  );

  const [initialState, setInitialState] = useState(memoizedEmptyPost);

  const onSuccess = useCallback(() => {
    addSnack('Post created ✅');
    setInitialState(emptyChallengePostGroups(challengeId, appUser.id, groupId));
  }, [addSnack, appUser.id, challengeId, groupId]);

  return (
    <EditChallengePostGroups
      isFirstCreate
      initialState={initialState}
      submitText='Post'
      onSuccess={onSuccess}
    />
  );
};
