import {ContextMenuDropdown, Grid, Text} from '@base-components';
import {CONTENT_CODES} from '@constants';
import {
  useChallengeTrainerMessageSelectGroupsActions,
  useCreateChallengeTrainerMessage,
} from '@contexts';
import {type BaseAppUser, type Challenge, patternMatch, type UUIDString} from '@types';
import {PostCancelOrSend, PostImage, PostInput} from '../BasePosts';

type ChallengeCreateTrainerMessageTeamsProps = {
  allGroups: {id: UUIDString; name: string}[] | undefined;
  challenge: Challenge;
  initialSelectedGroupId: UUIDString | undefined | null;
  initialSelectedTeamId: UUIDString | undefined | null;
  onSuccess: () => void;
  trainerAppUser: BaseAppUser;
};

export const ChallengeCreateTrainerMessage: React.FC<ChallengeCreateTrainerMessageTeamsProps> = ({
  allGroups,
  challenge,
  initialSelectedGroupId,
  initialSelectedTeamId,
  onSuccess,
  trainerAppUser,
}) => {
  const {
    hasChanged,
    isAllGroupsSelected,
    isImagePending,
    isNoGroupSelected,
    isPendingSubmit,
    isValid,
    onContentChange,
    onIdsChange,
    onImageChange,
    onImagePaste,
    onReset,
    onSubmit,
    selectedGroup,
    trainerMessagePost,
    type,
  } = useCreateChallengeTrainerMessage(challenge, trainerAppUser, onSuccess, {
    initialSelectedTeamId,
    initialSelectedGroupId,
    allGroups,
  });
  const isOnlyShowAll = patternMatch(type)
    .with('teams', () => initialSelectedTeamId === null)
    .with('groups', () => initialSelectedGroupId === null)
    .exhaustive();
  const contextActions = useChallengeTrainerMessageSelectGroupsActions(
    allGroups,
    onIdsChange,
    type,
  );
  const noTeamSelectedLabel = patternMatch(type)
    .with('teams', () => CONTENT_CODES().CHALLENGE.VIEW.TEAMS.NO_TEAM_SELECTED)
    .with('groups', () => CONTENT_CODES().CHALLENGE.VIEW.GROUPS.NO_GROUP_SELECTED)
    .exhaustive();
  const allTeamSelectedLabel = patternMatch(type)
    .with('teams', () => CONTENT_CODES().CHALLENGE.VIEW.TEAMS.ALL_TEAMS_SELECTED)
    .with('groups', () => CONTENT_CODES().CHALLENGE.VIEW.GROUPS.ALL_GROUPS_SELECTED)
    .exhaustive();
  const placeholderLabel = patternMatch(type)
    .with('teams', () => CONTENT_CODES().CHALLENGE.VIEW.TEAMS.TRAINER_MESSAGE_PLACEHOLDER)
    .with('groups', () => CONTENT_CODES().CHALLENGE.VIEW.GROUPS.TRAINER_MESSAGE_PLACEHOLDER)
    .exhaustive();

  return (
    <Grid container>
      {hasChanged && (
        <PostCancelOrSend
          isImagePending={isImagePending}
          isPendingSubmit={isPendingSubmit}
          isValid={isValid}
          submitLabel='Send message'
          onReset={onReset}
          onSubmit={onSubmit}
        />
      )}

      <Grid item px={1} py={1} xs={12}>
        {!isOnlyShowAll && (
          <ContextMenuDropdown
            actions={contextActions}
            label={
              // eslint-disable-next-line no-nested-ternary -- 3 states
              isNoGroupSelected
                ? noTeamSelectedLabel

                : (isAllGroupsSelected
                    ? allTeamSelectedLabel
                    : selectedGroup?.name)
            }
          />
        )}
        {isOnlyShowAll && (
          <Text textAlign='center' variant='titleLarge'>
            Challenge Broadcast
          </Text>
        )}
      </Grid>
      <PostInput
        hasPx
        appUserProfile={trainerAppUser}
        label={placeholderLabel}
        post={trainerMessagePost}
        onContentChange={onContentChange}
        onImagePaste={onImagePaste}
        onImageUrlChange={onImageChange}
      />
      <PostImage
        isImagePending={isImagePending}
        post={trainerMessagePost}
        onImageChange={onImageChange}
      />
    </Grid>
  );
};
