import {useImage} from 'expo-image';
import {Images, placeholderPostImage} from '@assets';
import {Box, ContextMenu, type ContextMenuActionInternal, IconButton, Text} from '@base-components';
import {useDimensions} from '@hooks';
import {type BaseAppUser, type BasePost, timestampToDate} from '@types';
import {formatFullTimestamp, useAppTheme} from '@utils';
import {useImageContextActions} from '@contexts';
import {LinkPreview} from './LinkPreview';

type PostCardProps<TPost extends BasePost> = {
  authorAppUser: BaseAppUser | undefined;
  contextActions: ContextMenuActionInternal[];
  showContextMenu: boolean;
  value: TPost;
};

export const PostCard: React.FC<PostCardProps<BasePost>> = ({
  authorAppUser,
  contextActions,
  showContextMenu,
  value,
}) => {
  const theme = useAppTheme();
  const {width} = useDimensions();
  const image = useImage(value.imageUrl || placeholderPostImage);
  const aspectRatio = image ? image.width / image.height : 1;
  const imageContextActions = useImageContextActions(value.imageUrl);

  return (
    <Box px={1} py={2} style={{backgroundColor: '#fff'}}>
      <Box flexDirection='row' justifyContent='space-between'>
        <Images.defaultProfile
          overrideSource={authorAppUser?.profilePicture}
          style={{
            width: 40,
            height: 40,
            borderRadius: 20,
            marginRight: 8,
          }}
        />
        <Box flexGrow={1}>
          <Text variant='labelMedium'>{authorAppUser ? `${authorAppUser.firstName} ${authorAppUser.lastName}` : ''}</Text>
          <Text style={{color: theme.colors.cardFadedTextColor}} variant='bodySmall'>
            {formatFullTimestamp(timestampToDate(value.createdDateTime))}
          </Text>
        </Box>
        <Box alignItems='flex-start' height='100%' justifyContent='flex-start'>
          {showContextMenu && (
            <ContextMenu actions={contextActions}>
              <IconButton
                icon='dots-horizontal'
                size={24}
                style={{marginTop: -8}}
              />
            </ContextMenu>
          )}
        </Box>
      </Box>

      {!!value.content && (
        <Box flexDirection='row' pr={2} pt={1}>
          <Text style={{flexWrap: 'wrap', flex: 1}} variant='bodyLarge'>
            {value.content}
          </Text>
        </Box>
      )}

      {value.imageUrl && (
        <Box mx={-1} pt={1}>
          <ContextMenu actions={imageContextActions}>
            <Images.placeholderPostImage
              contentFit='contain'
              overrideSource={value.imageUrl}
              style={{width, aspectRatio}}
            />
          </ContextMenu>
        </Box>
      )}

      {value.linkUrl && (
        <Box pt={1}>
          <LinkPreview url={value.linkUrl} />
        </Box>
      )}
    </Box>
  );
};
