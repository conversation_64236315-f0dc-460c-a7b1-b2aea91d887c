import {useMemo} from 'react';
import type {ImageChangeEvent} from 'react-native';
import {Box, Grid, IconButton, TextInputBare} from '@base-components';
import {onOpenGifKeyboard} from '@navigation';
import type {BaseAppUser, BasePost} from '@types';
import {UserProfile} from '../User';
import {LinkPreview} from './LinkPreview';

type PostInputProps = {
  appUserProfile: BaseAppUser;
  hasPx?: boolean | undefined;
  hideName?: boolean | undefined;
  label: string;
  onContentChange: (text: string) => void;
  onImagePaste: (e: ImageChangeEvent) => Promise<void>;
  onImageUrlChange: (options: {isRemove?: true | undefined}) => Promise<void>;
  placeholder?: string | undefined;
  post: BasePost;
};

export const PostInput: React.FC<PostInputProps> = ({
  appUserProfile,
  hasPx,
  hideName,
  label,
  onContentChange,
  onImagePaste,
  onImageUrlChange,
  placeholder = 'Type message here...',
  post,
}) => {
  const hasLink = !!post.linkUrl;

  return (
    <>
      <Grid item xs={2}>
        <UserProfile disablePress appUser={appUserProfile} hideName={hideName} />
      </Grid>
      <Grid item xs={10}>
        <TextInputBare
          label={label}
          placeholder={placeholder}
          value={post.content}
          onChangeText={onContentChange}
          onImageChange={useMemo(
            () => async (e: ImageChangeEvent) => {
              e.persist();
              await onImagePaste(e);
            },
            [onImagePaste],
          )}
        >
          {!hasLink &&
            <Box
              style={{
                backgroundColor: '#fff',
                borderColor: '#000',
                borderTopWidth: 0,
                flexDirection: 'row',
              }}
            >
              <IconButton icon='file-image-plus' onPress={() => onImageUrlChange({})} />
              <IconButton icon='file-gif-box' onPress={onOpenGifKeyboard} />
            </Box>}
        </TextInputBare>
      </Grid>
      {!!post.linkUrl &&
        <Grid item pb={1} px={hasPx ? 1 : 0} xs={12}>
          <LinkPreview url={post.linkUrl} />
        </Grid>}
    </>
  );
};
