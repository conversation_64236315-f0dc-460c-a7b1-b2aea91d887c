import {useCallback, useEffect, useRef, useState} from 'react';
import {useImageChange} from '@data-hooks';
import {type BasePost, type ImageUrl} from '@types';

/**
 * Helper function to extract first HTTPS URL from content
 */
const extractFirstHttpsUrl = (content: string): string | undefined => {
  const httpsUrlRegex = /https:\/\/[^\s]+/g;
  const urls = content.match(httpsUrlRegex);
  return urls?.[0];
};

 type BasePostHookConfig<TPost extends BasePost> = {
   imageUploadPathPrefix: `${string}/`;
   initialState: TPost;
   isPostEqual: (a: TPost, b: TPost) => boolean;
 };

/**
 * Shared hook for post content management with URL detection, image handling, and state management.
 * Provides common functionality for all post types that extend BasePost.
 */
export const useBasePost = <TPost extends BasePost>(config: BasePostHookConfig<TPost>) => {
  const {
    imageUploadPathPrefix,
    initialState,
    isPostEqual,
  } = config;

  // Track the initial state in order to reset the form when it changes
  const [initialStateImmutable, setInitialStateImmutable] = useState(initialState);
  const [post, setPost] = useState<TPost>(initialStateImmutable);

  // Reset the initial state when the initial state initializer changes
  const isSkipFirstRender = useRef(true);
  useEffect(() => {
    if (isSkipFirstRender.current) {
      isSkipFirstRender.current = false;
      return;
    }
    setInitialStateImmutable(initialState);
    setPost(initialState);
  }, [initialState]);

  /**
   * Content change handler with automatic HTTPS URL detection and linkUrl setting
   */
  const onContentChange = useCallback((content: string) => {
    const firstUrl = extractFirstHttpsUrl(content);

    setPost(prev => {
      // Check if content has actually changed
      if (prev.content === content && prev.linkUrl === firstUrl) return prev;

      const updatedPost = {...prev, content} as TPost;

      // Only set linkUrl if no image is present
      if (firstUrl && !prev.imageUrl) {
        updatedPost.linkUrl = firstUrl;
      } else if (!firstUrl) {
        delete updatedPost.linkUrl;
      }

      return updatedPost;
    });
  }, []);

  const removeImage = useCallback(() => {
    setPost(prev => {
      // If there's no image to remove, return same object
      if (!prev.imageUrl) return prev;

      const {imageUrl: _, ...rest} = prev;
      return rest as TPost;
    });
  }, []);

  const applyImage = useCallback((imageUrl: ImageUrl) => {
    setPost(prev => {
      // If linkUrl is present, disable image operations
      if (prev.linkUrl) return prev;

      // If the same image URL is already set, return same object
      if (prev.imageUrl === imageUrl) return prev;

      // Remove linkUrl when setting an image
      const {linkUrl: _, ...rest} = prev;
      return {...rest, imageUrl} as TPost;
    });
  }, []);

  const {isImagePending, onImageChange, onImagePaste} = useImageChange({
    imageUploadPathPrefix,
    removeImage,
    applyImage,
  });

  const onReset = useCallback(() => {
    setPost(initialStateImmutable);
  }, [initialStateImmutable]);

  const isChanged = !isPostEqual(initialStateImmutable, post);

  return {
    post,
    setPost,
    initialStateImmutable,
    setInitialStateImmutable,
    isChanged,
    onContentChange,
    isImagePending,
    onImageChange,
    onImagePaste,
    removeImage,
    applyImage,
    onReset,
  };
};
