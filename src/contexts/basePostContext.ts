import {useCallback, useEffect, useRef, useState} from 'react';
import {useImageChange} from '@data-hooks';
import {type BasePost, type ImageUrl} from '@types';

/**
 * Helper function to extract first HTTPS URL from content
 */
const extractFirstHttpsUrl = (content: string): string | undefined => {
  const httpsUrlRegex = /https:\/\/[^\s]+/g;
  const urls = content.match(httpsUrlRegex);
  const result = urls?.[0];
  console.log('[useBasePost] extractFirstHttpsUrl:', { content, result });
  return result;
};

 type BasePostHookConfig<TPost extends BasePost> = {
   imageUploadPathPrefix: `${string}/`;
   initialState: TPost;
   isPostEqual: (a: TPost, b: TPost) => boolean;
 };

/**
 * Shared hook for post content management with URL detection, image handling, and state management.
 * Provides common functionality for all post types that extend BasePost.
 */
export const useBasePost = <TPost extends BasePost>(config: BasePostHookConfig<TPost>) => {
  console.log('[useBasePost] Hook called with config:', {
    imageUploadPathPrefix: config.imageUploadPathPrefix,
    initialState: config.initialState,
    isPostEqual: config.isPostEqual.name
  });

  const {
    imageUploadPathPrefix,
    initialState,
    isPostEqual,
  } = config;

  // Track the initial state in order to reset the form when it changes
  const [initialStateImmutable, setInitialStateImmutable] = useState(initialState);
  const [post, setPost] = useState<TPost>(initialStateImmutable);

  console.log('[useBasePost] Current state:', {
    initialStateImmutable: { content: initialStateImmutable.content, linkUrl: initialStateImmutable.linkUrl },
    post: { content: post.content, linkUrl: post.linkUrl }
  });

  // Reset the initial state when the initial state initializer changes
  const isSkipFirstRender = useRef(true);
  useEffect(() => {
    console.log('[useBasePost] useEffect triggered - initialState changed');
    if (isSkipFirstRender.current) {
      isSkipFirstRender.current = false;
      console.log('[useBasePost] Skipping first render');
      return;
    }
    console.log('[useBasePost] Resetting state to new initialState');
    setInitialStateImmutable(initialState);
    setPost(initialState);
  }, [initialState]);

  /**
   * Content change handler with automatic HTTPS URL detection and linkUrl setting
   */
  const onContentChange = useCallback((content: string) => {
    console.log('[useBasePost] onContentChange called with:', content);
    const firstUrl = extractFirstHttpsUrl(content);

    setPost(prev => {
      console.log('[useBasePost] setPost callback - prev:', {
        content: prev.content,
        linkUrl: prev.linkUrl,
        imageUrl: prev.imageUrl
      });
      console.log('[useBasePost] setPost callback - new values:', { content, firstUrl });

      // Check if content has actually changed
      if (prev.content === content && prev.linkUrl === firstUrl) {
        console.log('[useBasePost] No changes detected, returning same object');
        return prev;
      }

      const updatedPost = {...prev, content} as TPost;

      // Only set linkUrl if no image is present
      if (firstUrl && !prev.imageUrl) {
        updatedPost.linkUrl = firstUrl;
        console.log('[useBasePost] Setting linkUrl:', firstUrl);
      } else if (!firstUrl) {
        delete updatedPost.linkUrl;
        console.log('[useBasePost] Removing linkUrl');
      }

      console.log('[useBasePost] Returning updated post:', {
        content: updatedPost.content,
        linkUrl: updatedPost.linkUrl,
        imageUrl: updatedPost.imageUrl
      });
      return updatedPost;
    });
  }, []);

  const removeImage = useCallback(() => {
    console.log('[useBasePost] removeImage called');
    setPost(prev => {
      // If there's no image to remove, return same object
      if (!prev.imageUrl) {
        console.log('[useBasePost] removeImage - no image to remove');
        return prev;
      }

      console.log('[useBasePost] removeImage - removing image');
      const {imageUrl: _, ...rest} = prev;
      return rest as TPost;
    });
  }, []);

  const applyImage = useCallback((imageUrl: ImageUrl) => {
    console.log('[useBasePost] applyImage called with:', imageUrl);
    setPost(prev => {
      // If linkUrl is present, disable image operations
      if (prev.linkUrl) {
        console.log('[useBasePost] applyImage - linkUrl present, skipping');
        return prev;
      }

      // If the same image URL is already set, return same object
      if (prev.imageUrl === imageUrl) {
        console.log('[useBasePost] applyImage - same image URL, returning same object');
        return prev;
      }

      console.log('[useBasePost] applyImage - applying new image');
      // Remove linkUrl when setting an image
      const {linkUrl: _, ...rest} = prev;
      return {...rest, imageUrl} as TPost;
    });
  }, []);

  const {isImagePending, onImageChange, onImagePaste} = useImageChange({
    imageUploadPathPrefix,
    removeImage,
    applyImage,
  });

  const onReset = useCallback(() => {
    console.log('[useBasePost] onReset called');
    setPost(initialStateImmutable);
  }, [initialStateImmutable]);

  const isChanged = !isPostEqual(initialStateImmutable, post);
  console.log('[useBasePost] isChanged calculation:', {
    isChanged,
    initialStateImmutable: { content: initialStateImmutable.content, linkUrl: initialStateImmutable.linkUrl },
    post: { content: post.content, linkUrl: post.linkUrl }
  });

  console.log('[useBasePost] Hook returning values');
  return {
    post,
    setPost,
    initialStateImmutable,
    setInitialStateImmutable,
    isChanged,
    onContentChange,
    isImagePending,
    onImageChange,
    onImagePaste,
    removeImage,
    applyImage,
    onReset,
  };
};
