import {useCallback} from 'react';
import {Box, ConditionalRender, ContextMenuDropdown, Text} from '@base-components';
import {
  CreateIcon,
  OrganizationPostsList,
  PostsListSkeleton,
  ScreenContent,
  ScreenHeader,
} from '@components';
import {
  ScreenWrapper,
  useAppUserOrganizationDocuments,
  useOrganizationById,
  useOrganizationPostCollection,
  useOrganizationSelectContextActions,
  usePersistedSelectedOrganizationId,
} from '@contexts';
import {type FeedScreenProps, useLinkTo} from '@navigation';
import {onHapticSuccess} from '@utils';

export const FeedScreen: React.FC<FeedScreenProps> = ({route}) => {
  const persistedOrganizationId = usePersistedSelectedOrganizationId();
  const {organizationId = persistedOrganizationId} = route.params ?? {
    organizationId: persistedOrganizationId,
  };
  const organization = useOrganizationById(organizationId);
  const organizations = useAppUserOrganizationDocuments();
  const contextActions = useOrganizationSelectContextActions(organizations);

  const {data: posts, refetch} = useOrganizationPostCollection(organizationId);
  const to = useLinkTo();

  const onHapticRefresh = useCallback(async () => {
    await refetch();
    await onHapticSuccess();
  }, [refetch]);

  return (
    <ScreenWrapper onRefresh={onHapticRefresh}>
      <ScreenHeader
        flexCenter={5}
        flexLeft={2}
        flexRight={2}
        left={<Box />}
        right={
          organizationId ? (
            <CreateIcon
              onPress={() => {
                to.createOrganizationPost({organizationId});
              }}
            />
          ) : (
            <Box />
          )
        }
        title={
          <ContextMenuDropdown
            actions={contextActions}
            label={
              organization && organizationId ? `${organization.name} Feed` : 'Organization Feed'
            }
          />
        }
      />

      <ScreenContent disableXPadding>
        {!organizationId && (
          <Box px={2}>
            <Text>Select an organization to view its feed</Text>
          </Box>
        )}

        <ConditionalRender
          condition={posts !== undefined}
          fallback={organizationId && <PostsListSkeleton />}
        >
          {organization &&
            <Box pt={1}>
              <OrganizationPostsList organization={organization} posts={posts} />
            </Box>}
        </ConditionalRender>
      </ScreenContent>
    </ScreenWrapper>
  );
};
