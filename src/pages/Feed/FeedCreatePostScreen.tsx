import {useCallback, useMemo} from 'react';
import {EditOrganizationPost, ScreenContent, ScreenHeader} from '@components';
import {ScreenWrapper, useAddSnack, useAppUserSafe} from '@contexts';
import {type FeedCreatePostScreenProps, useLinkTo} from '@navigation';
import {emptyOrganizationPost} from '@utils';

export const FeedCreatePostScreen: React.FC<FeedCreatePostScreenProps> = ({route}) => {
  const {organizationId} = route.params;
  const to = useLinkTo();
  const appUser = useAppUserSafe();
  const addSnack = useAddSnack();

  // Memoize the initial empty post to prevent new IDs on every render
  const memoizedEmptyPost = useMemo(
    () => emptyOrganizationPost(organizationId, appUser.id),
    [organizationId, appUser.id],
  );

  const onSuccess = useCallback(() => {
    addSnack('Post created successfully ✅');
    to.pop();
  }, [to, addSnack]);

  return (
    <ScreenWrapper isModalScreen>
      <ScreenHeader title='Create post' />

      <ScreenContent>
        <EditOrganizationPost
          isFirstCreate
          initialState={memoizedEmptyPost}
          submitText='Post'
          onSuccess={onSuccess}
        />
      </ScreenContent>
    </ScreenWrapper>
  );
};
